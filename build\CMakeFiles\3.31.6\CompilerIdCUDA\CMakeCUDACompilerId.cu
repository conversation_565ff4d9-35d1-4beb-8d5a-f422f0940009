#ifndef __CUDACC__
# error "A C or C++ compiler has been selected for CUDA"
#endif


/* Version number components: V=Version, R=Revision, P=Patch
   Version date components:   YYYY=Year, MM=Month,   DD=Day  */

#if defined(__NVCC__)
# define COMPILER_ID "NVIDIA"
# if defined(_MSC_VER)
#  define SIMULATE_ID "MSVC"
# elif defined(__clang__)
#  define SIMULATE_ID "Clang"
# elif defined(__GNUC__)
#  define SIMULATE_ID "GNU"
# endif
# if defined(__CUDACC_VER_MAJOR__)
#  define COMPILER_VERSION_MAJOR DEC(__CUDACC_VER_MAJOR__)
#  define COMPILER_VERSION_MINOR DEC(__CUDACC_VER_MINOR__)
#  define COMPILER_VERSION_PATCH DEC(__CUDACC_VER_BUILD__)
# endif
# if defined(_MSC_VER)
   /* _MSC_VER = VVRR */
#  define SIMULATE_VERSION_MAJOR DEC(_MSC_VER / 100)
#  define SIMULATE_VERSION_MINOR DEC(_MSC_VER % 100)
# elif defined(__clang__)
#  define SIMULATE_VERSION_MAJOR DEC(__clang_major__)
#  define SIMULATE_VERSION_MINOR DEC(__clang_minor__)
# elif defined(__GNUC__)
#  define SIMULATE_VERSION_MAJOR DEC(__GNUC__)
#  define SIMULATE_VERSION_MINOR DEC(__GNUC_MINOR__)
# endif

#elif defined(__clang__)
# define COMPILER_ID "Clang"
# if defined(_MSC_VER)
#  define SIMULATE_ID "MSVC"
# endif
# define COMPILER_VERSION_MAJOR DEC(__clang_major__)
# define COMPILER_VERSION_MINOR DEC(__clang_minor__)
# define COMPILER_VERSION_PATCH DEC(__clang_patchlevel__)
# if defined(_MSC_VER)
   /* _MSC_VER = VVRR */
#  define SIMULATE_VERSION_MAJOR DEC(_MSC_VER / 100)
#  define SIMULATE_VERSION_MINOR DEC(_MSC_VER % 100)
# endif


/* These compilers are either not known or too old to define an
  identification macro.  Try to identify the platform and guess that
  it is the native compiler.  */
#elif defined(__hpux) || defined(__hpua)
# define COMPILER_ID "HP"

#else /* unknown compiler */
# define COMPILER_ID ""
#endif

/* Detect host compiler used by NVCC. */
#ifdef __NVCC__

/* Version number components: V=Version, R=Revision, P=Patch
   Version date components:   YYYY=Year, MM=Month,   DD=Day  */

#if defined(__INTEL_COMPILER) || defined(__ICC)
# define HOST_COMPILER_ID "Intel"
# if defined(_MSC_VER)
#  define HOST_SIMULATE_ID "MSVC"
# endif
# if defined(__GNUC__)
#  define HOST_SIMULATE_ID "GNU"
# endif
  /* __INTEL_COMPILER = VRP prior to 2021, and then VVVV for 2021 and later,
     except that a few beta releases use the old format with V=2021.  */
# if __INTEL_COMPILER < 2021 || __INTEL_COMPILER == 202110 || __INTEL_COMPILER == 202111
#  define HOST_COMPILER_VERSION_MAJOR DEC(__INTEL_COMPILER/100)
#  define HOST_COMPILER_VERSION_MINOR DEC(__INTEL_COMPILER/10 % 10)
#  if defined(__INTEL_COMPILER_UPDATE)
#   define HOST_COMPILER_VERSION_PATCH DEC(__INTEL_COMPILER_UPDATE)
#  else
#   define HOST_COMPILER_VERSION_PATCH DEC(__INTEL_COMPILER   % 10)
#  endif
# else
#  define HOST_COMPILER_VERSION_MAJOR DEC(__INTEL_COMPILER)
#  define HOST_COMPILER_VERSION_MINOR DEC(__INTEL_COMPILER_UPDATE)
   /* The third version component from --version is an update index,
      but no macro is provided for it.  */
#  define HOST_COMPILER_VERSION_PATCH DEC(0)
# endif
# if defined(__INTEL_COMPILER_BUILD_DATE)
   /* __INTEL_COMPILER_BUILD_DATE = YYYYMMDD */
#  define HOST_COMPILER_VERSION_TWEAK DEC(__INTEL_COMPILER_BUILD_DATE)
# endif
# if defined(_MSC_VER)
   /* _MSC_VER = VVRR */
#  define HOST_SIMULATE_VERSION_MAJOR DEC(_MSC_VER / 100)
#  define HOST_SIMULATE_VERSION_MINOR DEC(_MSC_VER % 100)
# endif
# if defined(__GNUC__)
#  define HOST_SIMULATE_VERSION_MAJOR DEC(__GNUC__)
# elif defined(__GNUG__)
#  define HOST_SIMULATE_VERSION_MAJOR DEC(__GNUG__)
# endif
# if defined(__GNUC_MINOR__)
#  define HOST_SIMULATE_VERSION_MINOR DEC(__GNUC_MINOR__)
# endif
# if defined(__GNUC_PATCHLEVEL__)
#  define HOST_SIMULATE_VERSION_PATCH DEC(__GNUC_PATCHLEVEL__)
# endif

#elif (defined(__clang__) && defined(__INTEL_CLANG_COMPILER)) || defined(__INTEL_LLVM_COMPILER)
# define HOST_COMPILER_ID "IntelLLVM"
#if defined(_MSC_VER)
# define HOST_SIMULATE_ID "MSVC"
#endif
#if defined(__GNUC__)
# define HOST_SIMULATE_ID "GNU"
#endif
/* __INTEL_LLVM_COMPILER = VVVVRP prior to 2021.2.0, VVVVRRPP for 2021.2.0 and
 * later.  Look for 6 digit vs. 8 digit version number to decide encoding.
 * VVVV is no smaller than the current year when a version is released.
 */
#if __INTEL_LLVM_COMPILER < 1000000L
# define HOST_COMPILER_VERSION_MAJOR DEC(__INTEL_LLVM_COMPILER/100)
# define HOST_COMPILER_VERSION_MINOR DEC(__INTEL_LLVM_COMPILER/10 % 10)
# define HOST_COMPILER_VERSION_PATCH DEC(__INTEL_LLVM_COMPILER    % 10)
#else
# define HOST_COMPILER_VERSION_MAJOR DEC(__INTEL_LLVM_COMPILER/10000)
# define HOST_COMPILER_VERSION_MINOR DEC(__INTEL_LLVM_COMPILER/100 % 100)
# define HOST_COMPILER_VERSION_PATCH DEC(__INTEL_LLVM_COMPILER     % 100)
#endif
#if defined(_MSC_VER)
  /* _MSC_VER = VVRR */
# define HOST_SIMULATE_VERSION_MAJOR DEC(_MSC_VER / 100)
# define HOST_SIMULATE_VERSION_MINOR DEC(_MSC_VER % 100)
#endif
#if defined(__GNUC__)
# define HOST_SIMULATE_VERSION_MAJOR DEC(__GNUC__)
#elif defined(__GNUG__)
# define HOST_SIMULATE_VERSION_MAJOR DEC(__GNUG__)
#endif
#if defined(__GNUC_MINOR__)
# define HOST_SIMULATE_VERSION_MINOR DEC(__GNUC_MINOR__)
#endif
#if defined(__GNUC_PATCHLEVEL__)
# define HOST_SIMULATE_VERSION_PATCH DEC(__GNUC_PATCHLEVEL__)
#endif

#elif defined(__PATHCC__)
# define HOST_COMPILER_ID "PathScale"
# define HOST_COMPILER_VERSION_MAJOR DEC(__PATHCC__)
# define HOST_COMPILER_VERSION_MINOR DEC(__PATHCC_MINOR__)
# if defined(__PATHCC_PATCHLEVEL__)
#  define HOST_COMPILER_VERSION_PATCH DEC(__PATHCC_PATCHLEVEL__)
# endif

#elif defined(__BORLANDC__) && defined(__CODEGEARC_VERSION__)
# define HOST_COMPILER_ID "Embarcadero"
# define HOST_COMPILER_VERSION_MAJOR HEX(__CODEGEARC_VERSION__>>24 & 0x00FF)
# define HOST_COMPILER_VERSION_MINOR HEX(__CODEGEARC_VERSION__>>16 & 0x00FF)
# define HOST_COMPILER_VERSION_PATCH DEC(__CODEGEARC_VERSION__     & 0xFFFF)

#elif defined(__BORLANDC__)
# define HOST_COMPILER_ID "Borland"
  /* __BORLANDC__ = 0xVRR */
# define HOST_COMPILER_VERSION_MAJOR HEX(__BORLANDC__>>8)
# define HOST_COMPILER_VERSION_MINOR HEX(__BORLANDC__ & 0xFF)

#elif defined(__WATCOMC__) && __WATCOMC__ < 1200
# define HOST_COMPILER_ID "Watcom"
   /* __WATCOMC__ = VVRR */
# define HOST_COMPILER_VERSION_MAJOR DEC(__WATCOMC__ / 100)
# define HOST_COMPILER_VERSION_MINOR DEC((__WATCOMC__ / 10) % 10)
# if (__WATCOMC__ % 10) > 0
#  define HOST_COMPILER_VERSION_PATCH DEC(__WATCOMC__ % 10)
# endif

#elif defined(__WATCOMC__)
# define HOST_COMPILER_ID "OpenWatcom"
   /* __WATCOMC__ = VVRP + 1100 */
# define HOST_COMPILER_VERSION_MAJOR DEC((__WATCOMC__ - 1100) / 100)
# define HOST_COMPILER_VERSION_MINOR DEC((__WATCOMC__ / 10) % 10)
# if (__WATCOMC__ % 10) > 0
#  define HOST_COMPILER_VERSION_PATCH DEC(__WATCOMC__ % 10)
# endif

#elif defined(__SUNPRO_CC)
# define HOST_COMPILER_ID "SunPro"
# if __SUNPRO_CC >= 0x5100
   /* __SUNPRO_CC = 0xVRRP */
#  define HOST_COMPILER_VERSION_MAJOR HEX(__SUNPRO_CC>>12)
#  define HOST_COMPILER_VERSION_MINOR HEX(__SUNPRO_CC>>4 & 0xFF)
#  define HOST_COMPILER_VERSION_PATCH HEX(__SUNPRO_CC    & 0xF)
# else
   /* __SUNPRO_CC = 0xVRP */
#  define HOST_COMPILER_VERSION_MAJOR HEX(__SUNPRO_CC>>8)
#  define HOST_COMPILER_VERSION_MINOR HEX(__SUNPRO_CC>>4 & 0xF)
#  define HOST_COMPILER_VERSION_PATCH HEX(__SUNPRO_CC    & 0xF)
# endif

#elif defined(__HP_aCC)
# define HOST_COMPILER_ID "HP"
  /* __HP_aCC = VVRRPP */
# define HOST_COMPILER_VERSION_MAJOR DEC(__HP_aCC/10000)
# define HOST_COMPILER_VERSION_MINOR DEC(__HP_aCC/100 % 100)
# define HOST_COMPILER_VERSION_PATCH DEC(__HP_aCC     % 100)

#elif defined(__DECCXX)
# define HOST_COMPILER_ID "Compaq"
  /* __DECCXX_VER = VVRRTPPPP */
# define HOST_COMPILER_VERSION_MAJOR DEC(__DECCXX_VER/10000000)
# define HOST_COMPILER_VERSION_MINOR DEC(__DECCXX_VER/100000  % 100)
# define HOST_COMPILER_VERSION_PATCH DEC(__DECCXX_VER         % 10000)

#elif defined(__IBMCPP__) && defined(__COMPILER_VER__)
# define HOST_COMPILER_ID "zOS"
  /* __IBMCPP__ = VRP */
# define HOST_COMPILER_VERSION_MAJOR DEC(__IBMCPP__/100)
# define HOST_COMPILER_VERSION_MINOR DEC(__IBMCPP__/10 % 10)
# define HOST_COMPILER_VERSION_PATCH DEC(__IBMCPP__    % 10)

#elif defined(__open_xl__) && defined(__clang__)
# define HOST_COMPILER_ID "IBMClang"
# define HOST_COMPILER_VERSION_MAJOR DEC(__open_xl_version__)
# define HOST_COMPILER_VERSION_MINOR DEC(__open_xl_release__)
# define HOST_COMPILER_VERSION_PATCH DEC(__open_xl_modification__)
# define HOST_COMPILER_VERSION_TWEAK DEC(__open_xl_ptf_fix_level__)


#elif defined(__ibmxl__) && defined(__clang__)
# define HOST_COMPILER_ID "XLClang"
# define HOST_COMPILER_VERSION_MAJOR DEC(__ibmxl_version__)
# define HOST_COMPILER_VERSION_MINOR DEC(__ibmxl_release__)
# define HOST_COMPILER_VERSION_PATCH DEC(__ibmxl_modification__)
# define HOST_COMPILER_VERSION_TWEAK DEC(__ibmxl_ptf_fix_level__)


#elif defined(__IBMCPP__) && !defined(__COMPILER_VER__) && __IBMCPP__ >= 800
# define HOST_COMPILER_ID "XL"
  /* __IBMCPP__ = VRP */
# define HOST_COMPILER_VERSION_MAJOR DEC(__IBMCPP__/100)
# define HOST_COMPILER_VERSION_MINOR DEC(__IBMCPP__/10 % 10)
# define HOST_COMPILER_VERSION_PATCH DEC(__IBMCPP__    % 10)

#elif defined(__IBMCPP__) && !defined(__COMPILER_VER__) && __IBMCPP__ < 800
# define HOST_COMPILER_ID "VisualAge"
  /* __IBMCPP__ = VRP */
# define HOST_COMPILER_VERSION_MAJOR DEC(__IBMCPP__/100)
# define HOST_COMPILER_VERSION_MINOR DEC(__IBMCPP__/10 % 10)
# define HOST_COMPILER_VERSION_PATCH DEC(__IBMCPP__    % 10)

#elif defined(__NVCOMPILER)
# define HOST_COMPILER_ID "NVHPC"
# define HOST_COMPILER_VERSION_MAJOR DEC(__NVCOMPILER_MAJOR__)
# define HOST_COMPILER_VERSION_MINOR DEC(__NVCOMPILER_MINOR__)
# if defined(__NVCOMPILER_PATCHLEVEL__)
#  define HOST_COMPILER_VERSION_PATCH DEC(__NVCOMPILER_PATCHLEVEL__)
# endif

#elif defined(__PGI)
# define HOST_COMPILER_ID "PGI"
# define HOST_COMPILER_VERSION_MAJOR DEC(__PGIC__)
# define HOST_COMPILER_VERSION_MINOR DEC(__PGIC_MINOR__)
# if defined(__PGIC_PATCHLEVEL__)
#  define HOST_COMPILER_VERSION_PATCH DEC(__PGIC_PATCHLEVEL__)
# endif

#elif defined(__clang__) && defined(__cray__)
# define HOST_COMPILER_ID "CrayClang"
# define HOST_COMPILER_VERSION_MAJOR DEC(__cray_major__)
# define HOST_COMPILER_VERSION_MINOR DEC(__cray_minor__)
# define HOST_COMPILER_VERSION_PATCH DEC(__cray_patchlevel__)
# define HOST_COMPILER_VERSION_INTERNAL_STR __clang_version__


#elif defined(_CRAYC)
# define HOST_COMPILER_ID "Cray"
# define HOST_COMPILER_VERSION_MAJOR DEC(_RELEASE_MAJOR)
# define HOST_COMPILER_VERSION_MINOR DEC(_RELEASE_MINOR)

#elif defined(__TI_COMPILER_VERSION__)
# define HOST_COMPILER_ID "TI"
  /* __TI_COMPILER_VERSION__ = VVVRRRPPP */
# define HOST_COMPILER_VERSION_MAJOR DEC(__TI_COMPILER_VERSION__/1000000)
# define HOST_COMPILER_VERSION_MINOR DEC(__TI_COMPILER_VERSION__/1000   % 1000)
# define HOST_COMPILER_VERSION_PATCH DEC(__TI_COMPILER_VERSION__        % 1000)

#elif defined(__CLANG_FUJITSU)
# define HOST_COMPILER_ID "FujitsuClang"
# define HOST_COMPILER_VERSION_MAJOR DEC(__FCC_major__)
# define HOST_COMPILER_VERSION_MINOR DEC(__FCC_minor__)
# define HOST_COMPILER_VERSION_PATCH DEC(__FCC_patchlevel__)
# define HOST_COMPILER_VERSION_INTERNAL_STR __clang_version__


#elif defined(__FUJITSU)
# define HOST_COMPILER_ID "Fujitsu"
# if defined(__FCC_version__)
#   define HOST_COMPILER_VERSION __FCC_version__
# elif defined(__FCC_major__)
#   define HOST_COMPILER_VERSION_MAJOR DEC(__FCC_major__)
#   define HOST_COMPILER_VERSION_MINOR DEC(__FCC_minor__)
#   define HOST_COMPILER_VERSION_PATCH DEC(__FCC_patchlevel__)
# endif
# if defined(__fcc_version)
#   define HOST_COMPILER_VERSION_INTERNAL DEC(__fcc_version)
# elif defined(__FCC_VERSION)
#   define HOST_COMPILER_VERSION_INTERNAL DEC(__FCC_VERSION)
# endif


#elif defined(__ghs__)
# define HOST_COMPILER_ID "GHS"
/* __GHS_VERSION_NUMBER = VVVVRP */
# ifdef __GHS_VERSION_NUMBER
# define HOST_COMPILER_VERSION_MAJOR DEC(__GHS_VERSION_NUMBER / 100)
# define HOST_COMPILER_VERSION_MINOR DEC(__GHS_VERSION_NUMBER / 10 % 10)
# define HOST_COMPILER_VERSION_PATCH DEC(__GHS_VERSION_NUMBER      % 10)
# endif

#elif defined(__TASKING__)
# define HOST_COMPILER_ID "Tasking"
  # define HOST_COMPILER_VERSION_MAJOR DEC(__VERSION__/1000)
  # define HOST_COMPILER_VERSION_MINOR DEC(__VERSION__ % 100)
# define HOST_COMPILER_VERSION_INTERNAL DEC(__VERSION__)

#elif defined(__ORANGEC__)
# define HOST_COMPILER_ID "OrangeC"
# define HOST_COMPILER_VERSION_MAJOR DEC(__ORANGEC_MAJOR__)
# define HOST_COMPILER_VERSION_MINOR DEC(__ORANGEC_MINOR__)
# define HOST_COMPILER_VERSION_PATCH DEC(__ORANGEC_PATCHLEVEL__)

#elif defined(__SCO_VERSION__)
# define HOST_COMPILER_ID "SCO"

#elif defined(__ARMCC_VERSION) && !defined(__clang__)
# define HOST_COMPILER_ID "ARMCC"
#if __ARMCC_VERSION >= 1000000
  /* __ARMCC_VERSION = VRRPPPP */
  # define HOST_COMPILER_VERSION_MAJOR DEC(__ARMCC_VERSION/1000000)
  # define HOST_COMPILER_VERSION_MINOR DEC(__ARMCC_VERSION/10000 % 100)
  # define HOST_COMPILER_VERSION_PATCH DEC(__ARMCC_VERSION     % 10000)
#else
  /* __ARMCC_VERSION = VRPPPP */
  # define HOST_COMPILER_VERSION_MAJOR DEC(__ARMCC_VERSION/100000)
  # define HOST_COMPILER_VERSION_MINOR DEC(__ARMCC_VERSION/10000 % 10)
  # define HOST_COMPILER_VERSION_PATCH DEC(__ARMCC_VERSION    % 10000)
#endif


#elif defined(__clang__) && defined(__apple_build_version__)
# define HOST_COMPILER_ID "AppleClang"
# if defined(_MSC_VER)
#  define HOST_SIMULATE_ID "MSVC"
# endif
# define HOST_COMPILER_VERSION_MAJOR DEC(__clang_major__)
# define HOST_COMPILER_VERSION_MINOR DEC(__clang_minor__)
# define HOST_COMPILER_VERSION_PATCH DEC(__clang_patchlevel__)
# if defined(_MSC_VER)
   /* _MSC_VER = VVRR */
#  define HOST_SIMULATE_VERSION_MAJOR DEC(_MSC_VER / 100)
#  define HOST_SIMULATE_VERSION_MINOR DEC(_MSC_VER % 100)
# endif
# define HOST_COMPILER_VERSION_TWEAK DEC(__apple_build_version__)

#elif defined(__clang__) && defined(__ARMCOMPILER_VERSION)
# define HOST_COMPILER_ID "ARMClang"
  # define HOST_COMPILER_VERSION_MAJOR DEC(__ARMCOMPILER_VERSION/1000000)
  # define HOST_COMPILER_VERSION_MINOR DEC(__ARMCOMPILER_VERSION/10000 % 100)
  # define HOST_COMPILER_VERSION_PATCH DEC(__ARMCOMPILER_VERSION/100   % 100)
# define HOST_COMPILER_VERSION_INTERNAL DEC(__ARMCOMPILER_VERSION)

#elif defined(__clang__) && defined(__ti__)
# define HOST_COMPILER_ID "TIClang"
  # define HOST_COMPILER_VERSION_MAJOR DEC(__ti_major__)
  # define HOST_COMPILER_VERSION_MINOR DEC(__ti_minor__)
  # define HOST_COMPILER_VERSION_PATCH DEC(__ti_patchlevel__)
# define HOST_COMPILER_VERSION_INTERNAL DEC(__ti_version__)

#elif defined(__clang__)
# define HOST_COMPILER_ID "Clang"
# if defined(_MSC_VER)
#  define HOST_SIMULATE_ID "MSVC"
# endif
# define HOST_COMPILER_VERSION_MAJOR DEC(__clang_major__)
# define HOST_COMPILER_VERSION_MINOR DEC(__clang_minor__)
# define HOST_COMPILER_VERSION_PATCH DEC(__clang_patchlevel__)
# if defined(_MSC_VER)
   /* _MSC_VER = VVRR */
#  define HOST_SIMULATE_VERSION_MAJOR DEC(_MSC_VER / 100)
#  define HOST_SIMULATE_VERSION_MINOR DEC(_MSC_VER % 100)
# endif

#elif defined(__LCC__) && (defined(__GNUC__) || defined(__GNUG__) || defined(__MCST__))
# define HOST_COMPILER_ID "LCC"
# define HOST_COMPILER_VERSION_MAJOR DEC(__LCC__ / 100)
# define HOST_COMPILER_VERSION_MINOR DEC(__LCC__ % 100)
# if defined(__LCC_MINOR__)
#  define HOST_COMPILER_VERSION_PATCH DEC(__LCC_MINOR__)
# endif
# if defined(__GNUC__) && defined(__GNUC_MINOR__)
#  define HOST_SIMULATE_ID "GNU"
#  define HOST_SIMULATE_VERSION_MAJOR DEC(__GNUC__)
#  define HOST_SIMULATE_VERSION_MINOR DEC(__GNUC_MINOR__)
#  if defined(__GNUC_PATCHLEVEL__)
#   define HOST_SIMULATE_VERSION_PATCH DEC(__GNUC_PATCHLEVEL__)
#  endif
# endif

#elif defined(__GNUC__) || defined(__GNUG__)
# define HOST_COMPILER_ID "GNU"
# if defined(__GNUC__)
#  define HOST_COMPILER_VERSION_MAJOR DEC(__GNUC__)
# else
#  define HOST_COMPILER_VERSION_MAJOR DEC(__GNUG__)
# endif
# if defined(__GNUC_MINOR__)
#  define HOST_COMPILER_VERSION_MINOR DEC(__GNUC_MINOR__)
# endif
# if defined(__GNUC_PATCHLEVEL__)
#  define HOST_COMPILER_VERSION_PATCH DEC(__GNUC_PATCHLEVEL__)
# endif

#elif defined(_MSC_VER)
# define HOST_COMPILER_ID "MSVC"
  /* _MSC_VER = VVRR */
# define HOST_COMPILER_VERSION_MAJOR DEC(_MSC_VER / 100)
# define HOST_COMPILER_VERSION_MINOR DEC(_MSC_VER % 100)
# if defined(_MSC_FULL_VER)
#  if _MSC_VER >= 1400
    /* _MSC_FULL_VER = VVRRPPPPP */
#   define HOST_COMPILER_VERSION_PATCH DEC(_MSC_FULL_VER % 100000)
#  else
    /* _MSC_FULL_VER = VVRRPPPP */
#   define HOST_COMPILER_VERSION_PATCH DEC(_MSC_FULL_VER % 10000)
#  endif
# endif
# if defined(_MSC_BUILD)
#  define HOST_COMPILER_VERSION_TWEAK DEC(_MSC_BUILD)
# endif

#elif defined(_ADI_COMPILER)
# define HOST_COMPILER_ID "ADSP"
#if defined(__VERSIONNUM__)
  /* __VERSIONNUM__ = 0xVVRRPPTT */
#  define HOST_COMPILER_VERSION_MAJOR DEC(__VERSIONNUM__ >> 24 & 0xFF)
#  define HOST_COMPILER_VERSION_MINOR DEC(__VERSIONNUM__ >> 16 & 0xFF)
#  define HOST_COMPILER_VERSION_PATCH DEC(__VERSIONNUM__ >> 8 & 0xFF)
#  define HOST_COMPILER_VERSION_TWEAK DEC(__VERSIONNUM__ & 0xFF)
#endif

#elif defined(__IAR_SYSTEMS_ICC__) || defined(__IAR_SYSTEMS_ICC)
# define HOST_COMPILER_ID "IAR"
# if defined(__VER__) && defined(__ICCARM__)
#  define HOST_COMPILER_VERSION_MAJOR DEC((__VER__) / 1000000)
#  define HOST_COMPILER_VERSION_MINOR DEC(((__VER__) / 1000) % 1000)
#  define HOST_COMPILER_VERSION_PATCH DEC((__VER__) % 1000)
#  define HOST_COMPILER_VERSION_INTERNAL DEC(__IAR_SYSTEMS_ICC__)
# elif defined(__VER__) && (defined(__ICCAVR__) || defined(__ICCRX__) || defined(__ICCRH850__) || defined(__ICCRL78__) || defined(__ICC430__) || defined(__ICCRISCV__) || defined(__ICCV850__) || defined(__ICC8051__) || defined(__ICCSTM8__))
#  define HOST_COMPILER_VERSION_MAJOR DEC((__VER__) / 100)
#  define HOST_COMPILER_VERSION_MINOR DEC((__VER__) - (((__VER__) / 100)*100))
#  define HOST_COMPILER_VERSION_PATCH DEC(__SUBVERSION__)
#  define HOST_COMPILER_VERSION_INTERNAL DEC(__IAR_SYSTEMS_ICC__)
# endif


#endif
#endif /* __NVCC__ */


/* Construct the string literal in pieces to prevent the source from
   getting matched.  Store it in a pointer rather than an array
   because some compilers will just produce instructions to fill the
   array rather than assigning a pointer to a static array.  */
char const* info_compiler = "INFO" ":" "compiler[" COMPILER_ID "]";
#ifdef SIMULATE_ID
char const* info_simulate = "INFO" ":" "simulate[" SIMULATE_ID "]";
#endif

#define STRINGIFY_HELPER(X) #X
#define STRINGIFY(X) STRINGIFY_HELPER(X)

/* Identify known platforms by name.  */
#if defined(__linux) || defined(__linux__) || defined(linux)
# define PLATFORM_ID "Linux"

#elif defined(__MSYS__)
# define PLATFORM_ID "MSYS"

#elif defined(__CYGWIN__)
# define PLATFORM_ID "Cygwin"

#elif defined(__MINGW32__)
# define PLATFORM_ID "MinGW"

#elif defined(__APPLE__)
# define PLATFORM_ID "Darwin"

#elif defined(_WIN32) || defined(__WIN32__) || defined(WIN32)
# define PLATFORM_ID "Windows"

#elif defined(__FreeBSD__) || defined(__FreeBSD)
# define PLATFORM_ID "FreeBSD"

#elif defined(__NetBSD__) || defined(__NetBSD)
# define PLATFORM_ID "NetBSD"

#elif defined(__OpenBSD__) || defined(__OPENBSD)
# define PLATFORM_ID "OpenBSD"

#elif defined(__sun) || defined(sun)
# define PLATFORM_ID "SunOS"

#elif defined(_AIX) || defined(__AIX) || defined(__AIX__) || defined(__aix) || defined(__aix__)
# define PLATFORM_ID "AIX"

#elif defined(__hpux) || defined(__hpux__)
# define PLATFORM_ID "HP-UX"

#elif defined(__HAIKU__)
# define PLATFORM_ID "Haiku"

#elif defined(__BeOS) || defined(__BEOS__) || defined(_BEOS)
# define PLATFORM_ID "BeOS"

#elif defined(__QNX__) || defined(__QNXNTO__)
# define PLATFORM_ID "QNX"

#elif defined(__tru64) || defined(_tru64) || defined(__TRU64__)
# define PLATFORM_ID "Tru64"

#elif defined(__riscos) || defined(__riscos__)
# define PLATFORM_ID "RISCos"

#elif defined(__sinix) || defined(__sinix__) || defined(__SINIX__)
# define PLATFORM_ID "SINIX"

#elif defined(__UNIX_SV__)
# define PLATFORM_ID "UNIX_SV"

#elif defined(__bsdos__)
# define PLATFORM_ID "BSDOS"

#elif defined(_MPRAS) || defined(MPRAS)
# define PLATFORM_ID "MP-RAS"

#elif defined(__osf) || defined(__osf__)
# define PLATFORM_ID "OSF1"

#elif defined(_SCO_SV) || defined(SCO_SV) || defined(sco_sv)
# define PLATFORM_ID "SCO_SV"

#elif defined(__ultrix) || defined(__ultrix__) || defined(_ULTRIX)
# define PLATFORM_ID "ULTRIX"

#elif defined(__XENIX__) || defined(_XENIX) || defined(XENIX)
# define PLATFORM_ID "Xenix"

#elif defined(__WATCOMC__)
# if defined(__LINUX__)
#  define PLATFORM_ID "Linux"

# elif defined(__DOS__)
#  define PLATFORM_ID "DOS"

# elif defined(__OS2__)
#  define PLATFORM_ID "OS2"

# elif defined(__WINDOWS__)
#  define PLATFORM_ID "Windows3x"

# elif defined(__VXWORKS__)
#  define PLATFORM_ID "VxWorks"

# else /* unknown platform */
#  define PLATFORM_ID
# endif

#elif defined(__INTEGRITY)
# if defined(INT_178B)
#  define PLATFORM_ID "Integrity178"

# else /* regular Integrity */
#  define PLATFORM_ID "Integrity"
# endif

# elif defined(_ADI_COMPILER)
#  define PLATFORM_ID "ADSP"

#else /* unknown platform */
# define PLATFORM_ID

#endif

/* For windows compilers MSVC and Intel we can determine
   the architecture of the compiler being used.  This is because
   the compilers do not have flags that can change the architecture,
   but rather depend on which compiler is being used
*/
#if defined(_WIN32) && defined(_MSC_VER)
# if defined(_M_IA64)
#  define ARCHITECTURE_ID "IA64"

# elif defined(_M_ARM64EC)
#  define ARCHITECTURE_ID "ARM64EC"

# elif defined(_M_X64) || defined(_M_AMD64)
#  define ARCHITECTURE_ID "x64"

# elif defined(_M_IX86)
#  define ARCHITECTURE_ID "X86"

# elif defined(_M_ARM64)
#  define ARCHITECTURE_ID "ARM64"

# elif defined(_M_ARM)
#  if _M_ARM == 4
#   define ARCHITECTURE_ID "ARMV4I"
#  elif _M_ARM == 5
#   define ARCHITECTURE_ID "ARMV5I"
#  else
#   define ARCHITECTURE_ID "ARMV" STRINGIFY(_M_ARM)
#  endif

# elif defined(_M_MIPS)
#  define ARCHITECTURE_ID "MIPS"

# elif defined(_M_SH)
#  define ARCHITECTURE_ID "SHx"

# else /* unknown architecture */
#  define ARCHITECTURE_ID ""
# endif

#elif defined(__WATCOMC__)
# if defined(_M_I86)
#  define ARCHITECTURE_ID "I86"

# elif defined(_M_IX86)
#  define ARCHITECTURE_ID "X86"

# else /* unknown architecture */
#  define ARCHITECTURE_ID ""
# endif

#elif defined(__IAR_SYSTEMS_ICC__) || defined(__IAR_SYSTEMS_ICC)
# if defined(__ICCARM__)
#  define ARCHITECTURE_ID "ARM"

# elif defined(__ICCRX__)
#  define ARCHITECTURE_ID "RX"

# elif defined(__ICCRH850__)
#  define ARCHITECTURE_ID "RH850"

# elif defined(__ICCRL78__)
#  define ARCHITECTURE_ID "RL78"

# elif defined(__ICCRISCV__)
#  define ARCHITECTURE_ID "RISCV"

# elif defined(__ICCAVR__)
#  define ARCHITECTURE_ID "AVR"

# elif defined(__ICC430__)
#  define ARCHITECTURE_ID "MSP430"

# elif defined(__ICCV850__)
#  define ARCHITECTURE_ID "V850"

# elif defined(__ICC8051__)
#  define ARCHITECTURE_ID "8051"

# elif defined(__ICCSTM8__)
#  define ARCHITECTURE_ID "STM8"

# else /* unknown architecture */
#  define ARCHITECTURE_ID ""
# endif

#elif defined(__ghs__)
# if defined(__PPC64__)
#  define ARCHITECTURE_ID "PPC64"

# elif defined(__ppc__)
#  define ARCHITECTURE_ID "PPC"

# elif defined(__ARM__)
#  define ARCHITECTURE_ID "ARM"

# elif defined(__x86_64__)
#  define ARCHITECTURE_ID "x64"

# elif defined(__i386__)
#  define ARCHITECTURE_ID "X86"

# else /* unknown architecture */
#  define ARCHITECTURE_ID ""
# endif

#elif defined(__clang__) && defined(__ti__)
# if defined(__ARM_ARCH)
#  define ARCHITECTURE_ID "ARM"

# else /* unknown architecture */
#  define ARCHITECTURE_ID ""
# endif

#elif defined(__TI_COMPILER_VERSION__)
# if defined(__TI_ARM__)
#  define ARCHITECTURE_ID "ARM"

# elif defined(__MSP430__)
#  define ARCHITECTURE_ID "MSP430"

# elif defined(__TMS320C28XX__)
#  define ARCHITECTURE_ID "TMS320C28x"

# elif defined(__TMS320C6X__) || defined(_TMS320C6X)
#  define ARCHITECTURE_ID "TMS320C6x"

# else /* unknown architecture */
#  define ARCHITECTURE_ID ""
# endif

# elif defined(__ADSPSHARC__)
#  define ARCHITECTURE_ID "SHARC"

# elif defined(__ADSPBLACKFIN__)
#  define ARCHITECTURE_ID "Blackfin"

#elif defined(__TASKING__)

# if defined(__CTC__) || defined(__CPTC__)
#  define ARCHITECTURE_ID "TriCore"

# elif defined(__CMCS__)
#  define ARCHITECTURE_ID "MCS"

# elif defined(__CARM__)
#  define ARCHITECTURE_ID "ARM"

# elif defined(__CARC__)
#  define ARCHITECTURE_ID "ARC"

# elif defined(__C51__)
#  define ARCHITECTURE_ID "8051"

# elif defined(__CPCP__)
#  define ARCHITECTURE_ID "PCP"

# else
#  define ARCHITECTURE_ID ""
# endif

#else
#  define ARCHITECTURE_ID
#endif

/* Convert integer to decimal digit literals.  */
#define DEC(n)                   \
  ('0' + (((n) / 10000000)%10)), \
  ('0' + (((n) / 1000000)%10)),  \
  ('0' + (((n) / 100000)%10)),   \
  ('0' + (((n) / 10000)%10)),    \
  ('0' + (((n) / 1000)%10)),     \
  ('0' + (((n) / 100)%10)),      \
  ('0' + (((n) / 10)%10)),       \
  ('0' +  ((n) % 10))

/* Convert integer to hex digit literals.  */
#define HEX(n)             \
  ('0' + ((n)>>28 & 0xF)), \
  ('0' + ((n)>>24 & 0xF)), \
  ('0' + ((n)>>20 & 0xF)), \
  ('0' + ((n)>>16 & 0xF)), \
  ('0' + ((n)>>12 & 0xF)), \
  ('0' + ((n)>>8  & 0xF)), \
  ('0' + ((n)>>4  & 0xF)), \
  ('0' + ((n)     & 0xF))

/* Construct a string literal encoding the version number. */
#ifdef COMPILER_VERSION
char const* info_version = "INFO" ":" "compiler_version[" COMPILER_VERSION "]";

/* Construct a string literal encoding the version number components. */
#elif defined(COMPILER_VERSION_MAJOR)
char const info_version[] = {
  'I', 'N', 'F', 'O', ':',
  'c','o','m','p','i','l','e','r','_','v','e','r','s','i','o','n','[',
  COMPILER_VERSION_MAJOR,
# ifdef COMPILER_VERSION_MINOR
  '.', COMPILER_VERSION_MINOR,
#  ifdef COMPILER_VERSION_PATCH
   '.', COMPILER_VERSION_PATCH,
#   ifdef COMPILER_VERSION_TWEAK
    '.', COMPILER_VERSION_TWEAK,
#   endif
#  endif
# endif
  ']','\0'};
#endif

/* Construct a string literal encoding the internal version number. */
#ifdef COMPILER_VERSION_INTERNAL
char const info_version_internal[] = {
  'I', 'N', 'F', 'O', ':',
  'c','o','m','p','i','l','e','r','_','v','e','r','s','i','o','n','_',
  'i','n','t','e','r','n','a','l','[',
  COMPILER_VERSION_INTERNAL,']','\0'};
#elif defined(COMPILER_VERSION_INTERNAL_STR)
char const* info_version_internal = "INFO" ":" "compiler_version_internal[" COMPILER_VERSION_INTERNAL_STR "]";
#endif

/* Construct a string literal encoding the version number components. */
#ifdef SIMULATE_VERSION_MAJOR
char const info_simulate_version[] = {
  'I', 'N', 'F', 'O', ':',
  's','i','m','u','l','a','t','e','_','v','e','r','s','i','o','n','[',
  SIMULATE_VERSION_MAJOR,
# ifdef SIMULATE_VERSION_MINOR
  '.', SIMULATE_VERSION_MINOR,
#  ifdef SIMULATE_VERSION_PATCH
   '.', SIMULATE_VERSION_PATCH,
#   ifdef SIMULATE_VERSION_TWEAK
    '.', SIMULATE_VERSION_TWEAK,
#   endif
#  endif
# endif
  ']','\0'};
#endif

/* Construct the string literal in pieces to prevent the source from
   getting matched.  Store it in a pointer rather than an array
   because some compilers will just produce instructions to fill the
   array rather than assigning a pointer to a static array.  */
char const* info_platform = "INFO" ":" "platform[" PLATFORM_ID "]";
char const* info_arch = "INFO" ":" "arch[" ARCHITECTURE_ID "]";



#ifdef HOST_COMPILER_ID
char const* info_host_compiler = "INFO" ":" "host_compiler[" HOST_COMPILER_ID "]";
#endif
#ifdef HOST_COMPILER_VERSION
char const* info_host_compiler_version = "INFO" ":" "host_compiler_version[" HOST_COMPILER_VERSION "]";
#elif defined(HOST_COMPILER_VERSION_MAJOR)
char const info_host_compiler_version[] = {
  'I', 'N', 'F', 'O', ':','h','o','s','t','_',
  'c','o','m','p','i','l','e','r','_','v','e','r','s','i','o','n','[',
  HOST_COMPILER_VERSION_MAJOR,
# ifdef HOST_COMPILER_VERSION_MINOR
  '.', HOST_COMPILER_VERSION_MINOR,
#  ifdef HOST_COMPILER_VERSION_PATCH
   '.', HOST_COMPILER_VERSION_PATCH,
#   ifdef HOST_COMPILER_VERSION_TWEAK
    '.', HOST_COMPILER_VERSION_TWEAK,
#   endif
#  endif
# endif
  ']','\0'};
#endif

#define CXX_STD_11 201103L
#define CXX_STD_14 201402L
#define CXX_STD_17 201703L
#define CXX_STD_20 202002L
#define CXX_STD_23 202302L

#if defined(_MSC_VER) && defined(_MSVC_LANG)
#  if _MSVC_LANG > __cplusplus
#    define CXX_STD _MSVC_LANG
#  else
#    define CXX_STD __cplusplus
#  endif
#else
#  define CXX_STD __cplusplus
#endif

const char* info_language_standard_default = "INFO" ":" "standard_default["
#if CXX_STD > CXX_STD_23
  "26"
#elif CXX_STD > CXX_STD_20
  "23"
#elif CXX_STD > CXX_STD_17
  "20"
#elif CXX_STD > CXX_STD_14
  "17"
#elif CXX_STD > CXX_STD_11
  "14"
#elif CXX_STD >= CXX_STD_11
  "11"
#else
  "03"
#endif
"]";

const char* info_language_extensions_default = "INFO" ":" "extensions_default["
#if (defined(__clang__) || defined(__GNUC__)) &&                              \
  !defined(__STRICT_ANSI__)
  "ON"
#else
  "OFF"
#endif
"]";

/*--------------------------------------------------------------------------*/

int main(int argc, char* argv[])
{
  int require = 0;
  require += info_compiler[argc];
  require += info_platform[argc];
#ifdef COMPILER_VERSION_MAJOR
  require += info_version[argc];
#endif
#ifdef SIMULATE_ID
  require += info_simulate[argc];
#endif
#ifdef SIMULATE_VERSION_MAJOR
  require += info_simulate_version[argc];
#endif
#ifdef HOST_COMPILER_ID
  require += info_host_compiler[argc];
#endif
#ifdef HOST_COMPILER_VERSION_MAJOR
  require += info_host_compiler_version[argc];
#endif
  require += info_language_standard_default[argc];
  require += info_language_extensions_default[argc];
  (void)argv;
  return require;
}
