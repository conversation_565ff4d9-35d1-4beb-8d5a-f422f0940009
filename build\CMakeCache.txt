# This is the CMakeCache file.
# For build in directory: e:/study/AI_deploy/TensorRT/yolov8_seg/build
# It was generated by CMake: D:/cmake-3.31.6-windows-x86_64/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a program.
CMAKE_AR:FILEPATH=C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.38.33130/bin/Hostx64/x64/lib.exe

//No help, variable specified on the command line.
CMAKE_BUILD_TYPE:UNINITIALIZED=Release

//Semicolon separated list of supported configuration types, only
// supports Debug, Release, MinSizeRel, and RelWithDebInfo, anything
// else will be ignored.
CMAKE_CONFIGURATION_TYPES:STRING=Debug;Release;MinSizeRel;RelWithDebInfo

//Flags used by the CUDA compiler during all build types.
CMAKE_CUDA_FLAGS:STRING=-D_WINDOWS -Xcompiler=" /GR /EHsc"

//Flags used by the CUDA compiler during DEBUG builds.
CMAKE_CUDA_FLAGS_DEBUG:STRING=-Xcompiler=" -Zi -Ob0 -Od /RTC1"

//Flags used by the CUDA compiler during MINSIZEREL builds.
CMAKE_CUDA_FLAGS_MINSIZEREL:STRING=-Xcompiler="-O1 -Ob1" -DNDEBUG

//Flags used by the CUDA compiler during RELEASE builds.
CMAKE_CUDA_FLAGS_RELEASE:STRING=-Xcompiler="-O2 -Ob2" -DNDEBUG

//Flags used by the CUDA compiler during RELWITHDEBINFO builds.
CMAKE_CUDA_FLAGS_RELWITHDEBINFO:STRING=-Xcompiler=" -Zi -O2 -Ob1" -DNDEBUG

//Libraries linked by default with all CUDA applications.
CMAKE_CUDA_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=/DWIN32 /D_WINDOWS /GR /EHsc

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=/Zi /Ob0 /Od /RTC1

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=/O1 /Ob1 /DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=/O2 /Ob2 /DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=/Zi /O2 /Ob1 /DNDEBUG

//Libraries linked by default with all C++ applications.
CMAKE_CXX_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/pkgRedirects

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=C:/Program Files (x86)/yolov8_seg

//Path to a program.
CMAKE_LINKER:FILEPATH=C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.38.33130/bin/Hostx64/x64/link.exe

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Path to a program.
CMAKE_MT:FILEPATH=CMAKE_MT-NOTFOUND

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=yolov8_seg

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=1.0.0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//RC compiler
CMAKE_RC_COMPILER:FILEPATH=rc

//Flags for Windows Resource Compiler during all build types.
CMAKE_RC_FLAGS:STRING=-DWIN32

//Flags for Windows Resource Compiler during DEBUG builds.
CMAKE_RC_FLAGS_DEBUG:STRING=-D_DEBUG

//Flags for Windows Resource Compiler during MINSIZEREL builds.
CMAKE_RC_FLAGS_MINSIZEREL:STRING=

//Flags for Windows Resource Compiler during RELEASE builds.
CMAKE_RC_FLAGS_RELEASE:STRING=

//Flags for Windows Resource Compiler during RELWITHDEBINFO builds.
CMAKE_RC_FLAGS_RELWITHDEBINFO:STRING=

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Path to a file.
CUDAToolkit_CUPTI_INCLUDE_DIR:PATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/include/../extras/CUPTI/include

//Path to a library.
CUDA_CUDART:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/cudart.lib

//Path to a library.
CUDA_OpenCL_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/OpenCL.lib

//Path to a library.
CUDA_cuFile_LIBRARY:FILEPATH=CUDA_cuFile_LIBRARY-NOTFOUND

//Path to a library.
CUDA_cuFile_rdma_LIBRARY:FILEPATH=CUDA_cuFile_rdma_LIBRARY-NOTFOUND

//Path to a library.
CUDA_cuFile_rdma_static_LIBRARY:FILEPATH=CUDA_cuFile_rdma_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_cuFile_static_LIBRARY:FILEPATH=CUDA_cuFile_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_cublasLt_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/cublasLt.lib

//Path to a library.
CUDA_cublasLt_static_LIBRARY:FILEPATH=CUDA_cublasLt_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_cublas_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/cublas.lib

//Path to a library.
CUDA_cublas_static_LIBRARY:FILEPATH=CUDA_cublas_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_cuda_driver_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/cuda.lib

//Path to a library.
CUDA_cudart_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/cudart.lib

//Path to a library.
CUDA_cudart_static_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/cudart_static.lib

//Path to a library.
CUDA_cudla_LIBRARY:FILEPATH=CUDA_cudla_LIBRARY-NOTFOUND

//Path to a library.
CUDA_cufft_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/cufft.lib

//Path to a library.
CUDA_cufft_static_LIBRARY:FILEPATH=CUDA_cufft_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_cufft_static_nocallback_LIBRARY:FILEPATH=CUDA_cufft_static_nocallback_LIBRARY-NOTFOUND

//Path to a library.
CUDA_cufftw_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/cufftw.lib

//Path to a library.
CUDA_cufftw_static_LIBRARY:FILEPATH=CUDA_cufftw_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_culibos_LIBRARY:FILEPATH=CUDA_culibos_LIBRARY-NOTFOUND

//Path to a library.
CUDA_cupti_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/extras/CUPTI/lib64/cupti.lib

//Path to a library.
CUDA_cupti_static_LIBRARY:FILEPATH=CUDA_cupti_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_curand_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/curand.lib

//Path to a library.
CUDA_curand_static_LIBRARY:FILEPATH=CUDA_curand_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_cusolver_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/cusolver.lib

//Path to a library.
CUDA_cusolver_lapack_static_LIBRARY:FILEPATH=CUDA_cusolver_lapack_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_cusolver_metis_static_LIBRARY:FILEPATH=CUDA_cusolver_metis_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_cusolver_static_LIBRARY:FILEPATH=CUDA_cusolver_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_cusparse_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/cusparse.lib

//Path to a library.
CUDA_cusparse_static_LIBRARY:FILEPATH=CUDA_cusparse_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_nppc_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/nppc.lib

//Path to a library.
CUDA_nppc_static_LIBRARY:FILEPATH=CUDA_nppc_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_nppial_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/nppial.lib

//Path to a library.
CUDA_nppial_static_LIBRARY:FILEPATH=CUDA_nppial_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_nppicc_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/nppicc.lib

//Path to a library.
CUDA_nppicc_static_LIBRARY:FILEPATH=CUDA_nppicc_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_nppicom_LIBRARY:FILEPATH=CUDA_nppicom_LIBRARY-NOTFOUND

//Path to a library.
CUDA_nppicom_static_LIBRARY:FILEPATH=CUDA_nppicom_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_nppidei_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/nppidei.lib

//Path to a library.
CUDA_nppidei_static_LIBRARY:FILEPATH=CUDA_nppidei_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_nppif_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/nppif.lib

//Path to a library.
CUDA_nppif_static_LIBRARY:FILEPATH=CUDA_nppif_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_nppig_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/nppig.lib

//Path to a library.
CUDA_nppig_static_LIBRARY:FILEPATH=CUDA_nppig_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_nppim_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/nppim.lib

//Path to a library.
CUDA_nppim_static_LIBRARY:FILEPATH=CUDA_nppim_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_nppist_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/nppist.lib

//Path to a library.
CUDA_nppist_static_LIBRARY:FILEPATH=CUDA_nppist_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_nppisu_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/nppisu.lib

//Path to a library.
CUDA_nppisu_static_LIBRARY:FILEPATH=CUDA_nppisu_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_nppitc_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/nppitc.lib

//Path to a library.
CUDA_nppitc_static_LIBRARY:FILEPATH=CUDA_nppitc_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_npps_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/npps.lib

//Path to a library.
CUDA_npps_static_LIBRARY:FILEPATH=CUDA_npps_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_nvToolsExt_LIBRARY:FILEPATH=C:/Program Files/NVIDIA Corporation/NvToolsExt/lib/x64/nvToolsExt64_1.lib

//Path to a library.
CUDA_nvgraph_LIBRARY:FILEPATH=CUDA_nvgraph_LIBRARY-NOTFOUND

//Path to a library.
CUDA_nvgraph_static_LIBRARY:FILEPATH=CUDA_nvgraph_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_nvjpeg_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/nvjpeg.lib

//Path to a library.
CUDA_nvjpeg_static_LIBRARY:FILEPATH=CUDA_nvjpeg_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_nvml_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/nvml.lib

//Path to a library.
CUDA_nvml_static_LIBRARY:FILEPATH=CUDA_nvml_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_nvperf_host_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/extras/CUPTI/lib64/nvperf_host.lib

//Path to a library.
CUDA_nvperf_host_static_LIBRARY:FILEPATH=CUDA_nvperf_host_static_LIBRARY-NOTFOUND

//Path to a library.
CUDA_nvperf_target_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/extras/CUPTI/lib64/nvperf_target.lib

//Path to a library.
CUDA_nvptxcompiler_static_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/nvptxcompiler_static.lib

//Path to a library.
CUDA_nvrtc_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/nvrtc.lib

//Path to a library.
CUDA_nvrtc_builtins_LIBRARY:FILEPATH=CUDA_nvrtc_builtins_LIBRARY-NOTFOUND

//Path to a library.
CUDA_nvrtc_builtins_static_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/nvrtc-builtins_static.lib

//Path to a library.
CUDA_nvrtc_static_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/nvrtc_static.lib

//Path to a library.
CUDA_pcsamplingutil_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/extras/CUPTI/lib64/pcsamplingutil.lib

//No help, variable specified on the command line.
OpenCV_DIR:UNINITIALIZED=D:\opencv\build

//Path to a file.
TENSORRT_INCLUDE_DIR:PATH=C:/TensorRT-8.6.1.6/include

//Path to a library.
TENSORRT_LIB:FILEPATH=C:/TensorRT-8.6.1.6/lib/nvinfer.lib

//Path to a library.
TENSORRT_PARSER_LIB:FILEPATH=C:/TensorRT-8.6.1.6/lib/nvonnxparser.lib

//Path to a library.
TENSORRT_PLUGIN_LIB:FILEPATH=C:/TensorRT-8.6.1.6/lib/nvinfer_plugin.lib

//Value Computed by CMake
yolov8_seg_BINARY_DIR:STATIC=E:/study/AI_deploy/TensorRT/yolov8_seg/build

//Value Computed by CMake
yolov8_seg_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
yolov8_seg_SOURCE_DIR:STATIC=E:/study/AI_deploy/TensorRT/yolov8_seg


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=e:/study/AI_deploy/TensorRT/yolov8_seg/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=31
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=6
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=D:/cmake-3.31.6-windows-x86_64/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=D:/cmake-3.31.6-windows-x86_64/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=D:/cmake-3.31.6-windows-x86_64/bin/ctest.exe
//ADVANCED property for variable: CMAKE_CUDA_FLAGS
CMAKE_CUDA_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CUDA_FLAGS_DEBUG
CMAKE_CUDA_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CUDA_FLAGS_MINSIZEREL
CMAKE_CUDA_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CUDA_FLAGS_RELEASE
CMAKE_CUDA_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CUDA_FLAGS_RELWITHDEBINFO
CMAKE_CUDA_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CUDA_STANDARD_LIBRARIES
CMAKE_CUDA_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_STANDARD_LIBRARIES
CMAKE_CXX_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=Unknown
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Visual Studio 17 2022
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=C:/Program Files/Microsoft Visual Studio/2022/Community
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=E:/study/AI_deploy/TensorRT/yolov8_seg
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MT
CMAKE_MT-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//noop for ranlib
CMAKE_RANLIB:INTERNAL=:
//ADVANCED property for variable: CMAKE_RC_COMPILER
CMAKE_RC_COMPILER-ADVANCED:INTERNAL=1
CMAKE_RC_COMPILER_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS
CMAKE_RC_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_DEBUG
CMAKE_RC_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_MINSIZEREL
CMAKE_RC_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELEASE
CMAKE_RC_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELWITHDEBINFO
CMAKE_RC_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=D:/cmake-3.31.6-windows-x86_64/share/cmake-3.31
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDAToolkit_CUPTI_INCLUDE_DIR
CUDAToolkit_CUPTI_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_CUDART
CUDA_CUDART-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_OpenCL_LIBRARY
CUDA_OpenCL_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cuFile_LIBRARY
CUDA_cuFile_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cuFile_rdma_LIBRARY
CUDA_cuFile_rdma_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cuFile_rdma_static_LIBRARY
CUDA_cuFile_rdma_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cuFile_static_LIBRARY
CUDA_cuFile_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cublasLt_LIBRARY
CUDA_cublasLt_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cublasLt_static_LIBRARY
CUDA_cublasLt_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cublas_LIBRARY
CUDA_cublas_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cublas_static_LIBRARY
CUDA_cublas_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cuda_driver_LIBRARY
CUDA_cuda_driver_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cudart_LIBRARY
CUDA_cudart_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cudart_static_LIBRARY
CUDA_cudart_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cudla_LIBRARY
CUDA_cudla_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cufft_LIBRARY
CUDA_cufft_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cufft_static_LIBRARY
CUDA_cufft_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cufft_static_nocallback_LIBRARY
CUDA_cufft_static_nocallback_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cufftw_LIBRARY
CUDA_cufftw_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cufftw_static_LIBRARY
CUDA_cufftw_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_culibos_LIBRARY
CUDA_culibos_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cupti_LIBRARY
CUDA_cupti_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cupti_static_LIBRARY
CUDA_cupti_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_curand_LIBRARY
CUDA_curand_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_curand_static_LIBRARY
CUDA_curand_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cusolver_LIBRARY
CUDA_cusolver_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cusolver_lapack_static_LIBRARY
CUDA_cusolver_lapack_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cusolver_metis_static_LIBRARY
CUDA_cusolver_metis_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cusolver_static_LIBRARY
CUDA_cusolver_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cusparse_LIBRARY
CUDA_cusparse_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cusparse_static_LIBRARY
CUDA_cusparse_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppc_LIBRARY
CUDA_nppc_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppc_static_LIBRARY
CUDA_nppc_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppial_LIBRARY
CUDA_nppial_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppial_static_LIBRARY
CUDA_nppial_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppicc_LIBRARY
CUDA_nppicc_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppicc_static_LIBRARY
CUDA_nppicc_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppicom_LIBRARY
CUDA_nppicom_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppicom_static_LIBRARY
CUDA_nppicom_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppidei_LIBRARY
CUDA_nppidei_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppidei_static_LIBRARY
CUDA_nppidei_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppif_LIBRARY
CUDA_nppif_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppif_static_LIBRARY
CUDA_nppif_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppig_LIBRARY
CUDA_nppig_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppig_static_LIBRARY
CUDA_nppig_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppim_LIBRARY
CUDA_nppim_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppim_static_LIBRARY
CUDA_nppim_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppist_LIBRARY
CUDA_nppist_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppist_static_LIBRARY
CUDA_nppist_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppisu_LIBRARY
CUDA_nppisu_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppisu_static_LIBRARY
CUDA_nppisu_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppitc_LIBRARY
CUDA_nppitc_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppitc_static_LIBRARY
CUDA_nppitc_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_npps_LIBRARY
CUDA_npps_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_npps_static_LIBRARY
CUDA_npps_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvToolsExt_LIBRARY
CUDA_nvToolsExt_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvgraph_LIBRARY
CUDA_nvgraph_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvgraph_static_LIBRARY
CUDA_nvgraph_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvjpeg_LIBRARY
CUDA_nvjpeg_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvjpeg_static_LIBRARY
CUDA_nvjpeg_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvml_LIBRARY
CUDA_nvml_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvml_static_LIBRARY
CUDA_nvml_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvperf_host_LIBRARY
CUDA_nvperf_host_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvperf_host_static_LIBRARY
CUDA_nvperf_host_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvperf_target_LIBRARY
CUDA_nvperf_target_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvptxcompiler_static_LIBRARY
CUDA_nvptxcompiler_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvrtc_LIBRARY
CUDA_nvrtc_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvrtc_builtins_LIBRARY
CUDA_nvrtc_builtins_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvrtc_builtins_static_LIBRARY
CUDA_nvrtc_builtins_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvrtc_static_LIBRARY
CUDA_nvrtc_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_pcsamplingutil_LIBRARY
CUDA_pcsamplingutil_LIBRARY-ADVANCED:INTERNAL=1
//Details about finding CUDAToolkit
FIND_PACKAGE_MESSAGE_DETAILS_CUDAToolkit:INTERNAL=[C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/include][C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/lib/x64/cudart.lib][C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin][v11.8.89()]
//Details about finding OpenCV
FIND_PACKAGE_MESSAGE_DETAILS_OpenCV:INTERNAL=[D:/opencv/build][v4.5.5()]

