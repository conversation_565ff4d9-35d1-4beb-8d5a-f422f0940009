﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{2A79264D-188D-3A33-8B91-A9E6777D5593}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>yolov8_seg</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
    <Import Project="$(VCTargetsPath)\BuildCustomizations\CUDA 11.8.props" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\study\AI_deploy\TensorRT\yolov8_seg\build\bin\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">yolov8_seg.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">yolov8_seg</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\study\AI_deploy\TensorRT\yolov8_seg\build\bin\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">yolov8_seg.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">yolov8_seg</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\study\AI_deploy\TensorRT\yolov8_seg\build\bin\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">yolov8_seg.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">yolov8_seg</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\study\AI_deploy\TensorRT\yolov8_seg\build\bin\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">yolov8_seg.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">yolov8_seg</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\TensorRT-*******\include;E:\study\AI_deploy\TensorRT\yolov8_seg\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/opencv/build/include" /external:I "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_WARNINGS;NOMINMAX;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_CRT_SECURE_NO_WARNINGS;NOMINMAX;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\TensorRT-*******\include;E:\study\AI_deploy\TensorRT\yolov8_seg\include;D:\opencv\build\include;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\TensorRT-*******\include;E:\study\AI_deploy\TensorRT\yolov8_seg\include;D:\opencv\build\include;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>Copying D:/opencv/build/x64/vc15/bin/opencv_videoio_ffmpeg455_64.dll	Copying D:/opencv/build/x64/vc15/bin/opencv_videoio_msmf455_64.dll	Copying D:/opencv/build/x64/vc15/bin/opencv_videoio_msmf455_64d.dll	Copying D:/opencv/build/x64/vc15/bin/opencv_world455.dll	Copying D:/opencv/build/x64/vc15/bin/opencv_world455d.dll	Copying C:/TensorRT-*******/lib/nvinfer.dll	Copying C:/TensorRT-*******/lib/nvinfer_builder_resource.dll	Copying C:/TensorRT-*******/lib/nvinfer_dispatch.dll	Copying C:/TensorRT-*******/lib/nvinfer_lean.dll	Copying C:/TensorRT-*******/lib/nvinfer_plugin.dll	Copying C:/TensorRT-*******/lib/nvinfer_vc_plugin.dll	Copying C:/TensorRT-*******/lib/nvonnxparser.dll	Copying C:/TensorRT-*******/lib/nvparsers.dll	Copying C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/cudart32_110.dll	Copying C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/cudart64_110.dll</Message>
      <Command>setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different D:/opencv/build/x64/vc15/bin/opencv_videoio_ffmpeg455_64.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different D:/opencv/build/x64/vc15/bin/opencv_videoio_msmf455_64.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different D:/opencv/build/x64/vc15/bin/opencv_videoio_msmf455_64d.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different D:/opencv/build/x64/vc15/bin/opencv_world455.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different D:/opencv/build/x64/vc15/bin/opencv_world455d.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer_builder_resource.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer_dispatch.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer_lean.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer_plugin.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer_vc_plugin.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvonnxparser.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvparsers.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/cudart32_110.dll" E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/cudart64_110.dll" E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>D:\opencv\build\x64\vc15\lib\opencv_world455d.lib;D:\opencv\build\x64\vc15\lib\opencv_world455d.lib;D:\opencv\build\x64\vc15\lib\opencv_world455d.lib;D:\opencv\build\x64\vc15\lib\opencv_world455d.lib;D:\opencv\build\x64\vc15\lib\opencv_world455d.lib;D:\opencv\build\x64\vc15\lib\opencv_world455d.lib;D:\opencv\build\x64\vc15\lib\opencv_world455d.lib;D:\opencv\build\x64\vc15\lib\opencv_world455d.lib;D:\opencv\build\x64\vc15\lib\opencv_world455d.lib;D:\opencv\build\x64\vc15\lib\opencv_world455d.lib;D:\opencv\build\x64\vc15\lib\opencv_world455d.lib;D:\opencv\build\x64\vc15\lib\opencv_world455d.lib;D:\opencv\build\x64\vc15\lib\opencv_world455d.lib;D:\opencv\build\x64\vc15\lib\opencv_world455d.lib;D:\opencv\build\x64\vc15\lib\opencv_world455d.lib;D:\opencv\build\x64\vc15\lib\opencv_world455d.lib;C:\TensorRT-*******\lib\nvinfer.lib;C:\TensorRT-*******\lib\nvonnxparser.lib;C:\TensorRT-*******\lib\nvinfer_plugin.lib;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\lib\x64\cudart.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/study/AI_deploy/TensorRT/yolov8_seg/build/Debug/yolov8_seg.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Debug/yolov8_seg.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <CudaLink>
      <AdditionalOptions>-forward-unknown-to-host-compiler -Wno-deprecated-gpu-targets </AdditionalOptions>
      <PerformDeviceLink>false</PerformDeviceLink>
    </CudaLink>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\TensorRT-*******\include;E:\study\AI_deploy\TensorRT\yolov8_seg\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/opencv/build/include" /external:I "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CRT_SECURE_NO_WARNINGS;NOMINMAX;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CRT_SECURE_NO_WARNINGS;NOMINMAX;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\TensorRT-*******\include;E:\study\AI_deploy\TensorRT\yolov8_seg\include;D:\opencv\build\include;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\TensorRT-*******\include;E:\study\AI_deploy\TensorRT\yolov8_seg\include;D:\opencv\build\include;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>Copying D:/opencv/build/x64/vc15/bin/opencv_videoio_ffmpeg455_64.dll	Copying D:/opencv/build/x64/vc15/bin/opencv_videoio_msmf455_64.dll	Copying D:/opencv/build/x64/vc15/bin/opencv_videoio_msmf455_64d.dll	Copying D:/opencv/build/x64/vc15/bin/opencv_world455.dll	Copying D:/opencv/build/x64/vc15/bin/opencv_world455d.dll	Copying C:/TensorRT-*******/lib/nvinfer.dll	Copying C:/TensorRT-*******/lib/nvinfer_builder_resource.dll	Copying C:/TensorRT-*******/lib/nvinfer_dispatch.dll	Copying C:/TensorRT-*******/lib/nvinfer_lean.dll	Copying C:/TensorRT-*******/lib/nvinfer_plugin.dll	Copying C:/TensorRT-*******/lib/nvinfer_vc_plugin.dll	Copying C:/TensorRT-*******/lib/nvonnxparser.dll	Copying C:/TensorRT-*******/lib/nvparsers.dll	Copying C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/cudart32_110.dll	Copying C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/cudart64_110.dll</Message>
      <Command>setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different D:/opencv/build/x64/vc15/bin/opencv_videoio_ffmpeg455_64.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different D:/opencv/build/x64/vc15/bin/opencv_videoio_msmf455_64.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different D:/opencv/build/x64/vc15/bin/opencv_videoio_msmf455_64d.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different D:/opencv/build/x64/vc15/bin/opencv_world455.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different D:/opencv/build/x64/vc15/bin/opencv_world455d.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer_builder_resource.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer_dispatch.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer_lean.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer_plugin.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer_vc_plugin.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvonnxparser.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvparsers.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/cudart32_110.dll" E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/cudart64_110.dll" E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;C:\TensorRT-*******\lib\nvinfer.lib;C:\TensorRT-*******\lib\nvonnxparser.lib;C:\TensorRT-*******\lib\nvinfer_plugin.lib;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\lib\x64\cudart.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/study/AI_deploy/TensorRT/yolov8_seg/build/Release/yolov8_seg.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/Release/yolov8_seg.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <CudaLink>
      <AdditionalOptions>-forward-unknown-to-host-compiler -Wno-deprecated-gpu-targets </AdditionalOptions>
      <PerformDeviceLink>false</PerformDeviceLink>
    </CudaLink>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\TensorRT-*******\include;E:\study\AI_deploy\TensorRT\yolov8_seg\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/opencv/build/include" /external:I "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CRT_SECURE_NO_WARNINGS;NOMINMAX;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CRT_SECURE_NO_WARNINGS;NOMINMAX;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\TensorRT-*******\include;E:\study\AI_deploy\TensorRT\yolov8_seg\include;D:\opencv\build\include;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\TensorRT-*******\include;E:\study\AI_deploy\TensorRT\yolov8_seg\include;D:\opencv\build\include;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>Copying D:/opencv/build/x64/vc15/bin/opencv_videoio_ffmpeg455_64.dll	Copying D:/opencv/build/x64/vc15/bin/opencv_videoio_msmf455_64.dll	Copying D:/opencv/build/x64/vc15/bin/opencv_videoio_msmf455_64d.dll	Copying D:/opencv/build/x64/vc15/bin/opencv_world455.dll	Copying D:/opencv/build/x64/vc15/bin/opencv_world455d.dll	Copying C:/TensorRT-*******/lib/nvinfer.dll	Copying C:/TensorRT-*******/lib/nvinfer_builder_resource.dll	Copying C:/TensorRT-*******/lib/nvinfer_dispatch.dll	Copying C:/TensorRT-*******/lib/nvinfer_lean.dll	Copying C:/TensorRT-*******/lib/nvinfer_plugin.dll	Copying C:/TensorRT-*******/lib/nvinfer_vc_plugin.dll	Copying C:/TensorRT-*******/lib/nvonnxparser.dll	Copying C:/TensorRT-*******/lib/nvparsers.dll	Copying C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/cudart32_110.dll	Copying C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/cudart64_110.dll</Message>
      <Command>setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different D:/opencv/build/x64/vc15/bin/opencv_videoio_ffmpeg455_64.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different D:/opencv/build/x64/vc15/bin/opencv_videoio_msmf455_64.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different D:/opencv/build/x64/vc15/bin/opencv_videoio_msmf455_64d.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different D:/opencv/build/x64/vc15/bin/opencv_world455.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different D:/opencv/build/x64/vc15/bin/opencv_world455d.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer_builder_resource.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer_dispatch.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer_lean.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer_plugin.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer_vc_plugin.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvonnxparser.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvparsers.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/cudart32_110.dll" E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/cudart64_110.dll" E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;C:\TensorRT-*******\lib\nvinfer.lib;C:\TensorRT-*******\lib\nvonnxparser.lib;C:\TensorRT-*******\lib\nvinfer_plugin.lib;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\lib\x64\cudart.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/study/AI_deploy/TensorRT/yolov8_seg/build/MinSizeRel/yolov8_seg.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/MinSizeRel/yolov8_seg.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <CudaLink>
      <AdditionalOptions>-forward-unknown-to-host-compiler -Wno-deprecated-gpu-targets </AdditionalOptions>
      <PerformDeviceLink>false</PerformDeviceLink>
    </CudaLink>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\TensorRT-*******\include;E:\study\AI_deploy\TensorRT\yolov8_seg\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/opencv/build/include" /external:I "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CRT_SECURE_NO_WARNINGS;NOMINMAX;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CRT_SECURE_NO_WARNINGS;NOMINMAX;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\TensorRT-*******\include;E:\study\AI_deploy\TensorRT\yolov8_seg\include;D:\opencv\build\include;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\TensorRT-*******\include;E:\study\AI_deploy\TensorRT\yolov8_seg\include;D:\opencv\build\include;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>Copying D:/opencv/build/x64/vc15/bin/opencv_videoio_ffmpeg455_64.dll	Copying D:/opencv/build/x64/vc15/bin/opencv_videoio_msmf455_64.dll	Copying D:/opencv/build/x64/vc15/bin/opencv_videoio_msmf455_64d.dll	Copying D:/opencv/build/x64/vc15/bin/opencv_world455.dll	Copying D:/opencv/build/x64/vc15/bin/opencv_world455d.dll	Copying C:/TensorRT-*******/lib/nvinfer.dll	Copying C:/TensorRT-*******/lib/nvinfer_builder_resource.dll	Copying C:/TensorRT-*******/lib/nvinfer_dispatch.dll	Copying C:/TensorRT-*******/lib/nvinfer_lean.dll	Copying C:/TensorRT-*******/lib/nvinfer_plugin.dll	Copying C:/TensorRT-*******/lib/nvinfer_vc_plugin.dll	Copying C:/TensorRT-*******/lib/nvonnxparser.dll	Copying C:/TensorRT-*******/lib/nvparsers.dll	Copying C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/cudart32_110.dll	Copying C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/cudart64_110.dll</Message>
      <Command>setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different D:/opencv/build/x64/vc15/bin/opencv_videoio_ffmpeg455_64.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different D:/opencv/build/x64/vc15/bin/opencv_videoio_msmf455_64.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different D:/opencv/build/x64/vc15/bin/opencv_videoio_msmf455_64d.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different D:/opencv/build/x64/vc15/bin/opencv_world455.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different D:/opencv/build/x64/vc15/bin/opencv_world455d.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer_builder_resource.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer_dispatch.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer_lean.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer_plugin.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvinfer_vc_plugin.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvonnxparser.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different C:/TensorRT-*******/lib/nvparsers.dll E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/cudart32_110.dll" E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -E copy_if_different "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/cudart64_110.dll" E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;D:\opencv\build\x64\vc15\lib\opencv_world455.lib;C:\TensorRT-*******\lib\nvinfer.lib;C:\TensorRT-*******\lib\nvonnxparser.lib;C:\TensorRT-*******\lib\nvinfer_plugin.lib;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\lib\x64\cudart.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/study/AI_deploy/TensorRT/yolov8_seg/build/RelWithDebInfo/yolov8_seg.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/study/AI_deploy/TensorRT/yolov8_seg/build/bin/RelWithDebInfo/yolov8_seg.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <CudaLink>
      <AdditionalOptions>-forward-unknown-to-host-compiler -Wno-deprecated-gpu-targets </AdditionalOptions>
      <PerformDeviceLink>false</PerformDeviceLink>
    </CudaLink>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="E:\study\AI_deploy\TensorRT\yolov8_seg\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule E:/study/AI_deploy/TensorRT/yolov8_seg/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -SE:/study/AI_deploy/TensorRT/yolov8_seg -BE:/study/AI_deploy/TensorRT/yolov8_seg/build --check-stamp-file E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCUDACompiler.cmake.in;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCUDACompilerABI.cu;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCUDAInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCXXCompiler.cmake.in;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCXXCompilerABI.cpp;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCompilerIdDetection.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCUDACompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCXXCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCompilerABI.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCompilerId.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCompilerSupport.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineRCCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineSystem.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeFindBinUtils.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeParseImplicitIncludeInfo.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeParseLibraryArchitecture.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeRCCompiler.cmake.in;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeRCInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeSystem.cmake.in;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeTestCUDACompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeTestCXXCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeTestCompilerCommon.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeTestRCCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\ADSP-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\ARMCC-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\ARMClang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\AppleClang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Borland-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Cray-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\CrayClang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\GHS-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\GNU-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\HP-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IAR-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Intel-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\LCC-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\MSVC-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\MSVC.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\NVHPC-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\NVIDIA-CUDA.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\NVIDIA.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\OrangeC-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\PGI-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\PathScale-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\SCO-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\TI-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\TIClang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Tasking-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Watcom-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\XL-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\zOS-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CompilerId\VS-10.vcxproj.in;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\FindCUDAToolkit.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\FindPackageMessage.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCUDAArchitecturesAll.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCUDAArchitecturesNative.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCUDAArchitecturesValidate.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCUDAFilterImplicitLibs.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCUDALinkerInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeDetermineLinkerId.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeNVCCFilterImplicitInfo.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeNVCCParseImplicitInfo.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\FeatureTesting.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CUDA.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows-Determine-CXX.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows-NVIDIA-CUDA.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;D:\opencv\build\OpenCVConfig-version.cmake;D:\opencv\build\OpenCVConfig.cmake;D:\opencv\build\x64\vc15\lib\OpenCVConfig.cmake;D:\opencv\build\x64\vc15\lib\OpenCVModules-debug.cmake;D:\opencv\build\x64\vc15\lib\OpenCVModules-release.cmake;D:\opencv\build\x64\vc15\lib\OpenCVModules.cmake;E:\study\AI_deploy\TensorRT\yolov8_seg\build\CMakeFiles\3.31.6\CMakeCUDACompiler.cmake;E:\study\AI_deploy\TensorRT\yolov8_seg\build\CMakeFiles\3.31.6\CMakeCXXCompiler.cmake;E:\study\AI_deploy\TensorRT\yolov8_seg\build\CMakeFiles\3.31.6\CMakeRCCompiler.cmake;E:\study\AI_deploy\TensorRT\yolov8_seg\build\CMakeFiles\3.31.6\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\study\AI_deploy\TensorRT\yolov8_seg\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule E:/study/AI_deploy/TensorRT/yolov8_seg/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -SE:/study/AI_deploy/TensorRT/yolov8_seg -BE:/study/AI_deploy/TensorRT/yolov8_seg/build --check-stamp-file E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCUDACompiler.cmake.in;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCUDACompilerABI.cu;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCUDAInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCXXCompiler.cmake.in;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCXXCompilerABI.cpp;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCompilerIdDetection.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCUDACompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCXXCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCompilerABI.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCompilerId.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCompilerSupport.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineRCCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineSystem.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeFindBinUtils.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeParseImplicitIncludeInfo.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeParseLibraryArchitecture.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeRCCompiler.cmake.in;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeRCInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeSystem.cmake.in;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeTestCUDACompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeTestCXXCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeTestCompilerCommon.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeTestRCCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\ADSP-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\ARMCC-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\ARMClang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\AppleClang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Borland-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Cray-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\CrayClang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\GHS-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\GNU-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\HP-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IAR-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Intel-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\LCC-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\MSVC-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\MSVC.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\NVHPC-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\NVIDIA-CUDA.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\NVIDIA.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\OrangeC-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\PGI-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\PathScale-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\SCO-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\TI-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\TIClang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Tasking-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Watcom-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\XL-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\zOS-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CompilerId\VS-10.vcxproj.in;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\FindCUDAToolkit.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\FindPackageMessage.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCUDAArchitecturesAll.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCUDAArchitecturesNative.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCUDAArchitecturesValidate.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCUDAFilterImplicitLibs.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCUDALinkerInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeDetermineLinkerId.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeNVCCFilterImplicitInfo.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeNVCCParseImplicitInfo.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\FeatureTesting.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CUDA.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows-Determine-CXX.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows-NVIDIA-CUDA.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;D:\opencv\build\OpenCVConfig-version.cmake;D:\opencv\build\OpenCVConfig.cmake;D:\opencv\build\x64\vc15\lib\OpenCVConfig.cmake;D:\opencv\build\x64\vc15\lib\OpenCVModules-debug.cmake;D:\opencv\build\x64\vc15\lib\OpenCVModules-release.cmake;D:\opencv\build\x64\vc15\lib\OpenCVModules.cmake;E:\study\AI_deploy\TensorRT\yolov8_seg\build\CMakeFiles\3.31.6\CMakeCUDACompiler.cmake;E:\study\AI_deploy\TensorRT\yolov8_seg\build\CMakeFiles\3.31.6\CMakeCXXCompiler.cmake;E:\study\AI_deploy\TensorRT\yolov8_seg\build\CMakeFiles\3.31.6\CMakeRCCompiler.cmake;E:\study\AI_deploy\TensorRT\yolov8_seg\build\CMakeFiles\3.31.6\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\study\AI_deploy\TensorRT\yolov8_seg\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule E:/study/AI_deploy/TensorRT/yolov8_seg/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -SE:/study/AI_deploy/TensorRT/yolov8_seg -BE:/study/AI_deploy/TensorRT/yolov8_seg/build --check-stamp-file E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCUDACompiler.cmake.in;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCUDACompilerABI.cu;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCUDAInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCXXCompiler.cmake.in;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCXXCompilerABI.cpp;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCompilerIdDetection.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCUDACompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCXXCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCompilerABI.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCompilerId.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCompilerSupport.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineRCCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineSystem.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeFindBinUtils.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeParseImplicitIncludeInfo.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeParseLibraryArchitecture.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeRCCompiler.cmake.in;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeRCInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeSystem.cmake.in;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeTestCUDACompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeTestCXXCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeTestCompilerCommon.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeTestRCCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\ADSP-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\ARMCC-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\ARMClang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\AppleClang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Borland-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Cray-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\CrayClang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\GHS-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\GNU-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\HP-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IAR-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Intel-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\LCC-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\MSVC-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\MSVC.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\NVHPC-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\NVIDIA-CUDA.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\NVIDIA.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\OrangeC-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\PGI-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\PathScale-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\SCO-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\TI-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\TIClang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Tasking-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Watcom-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\XL-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\zOS-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CompilerId\VS-10.vcxproj.in;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\FindCUDAToolkit.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\FindPackageMessage.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCUDAArchitecturesAll.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCUDAArchitecturesNative.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCUDAArchitecturesValidate.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCUDAFilterImplicitLibs.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCUDALinkerInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeDetermineLinkerId.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeNVCCFilterImplicitInfo.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeNVCCParseImplicitInfo.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\FeatureTesting.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CUDA.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows-Determine-CXX.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows-NVIDIA-CUDA.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;D:\opencv\build\OpenCVConfig-version.cmake;D:\opencv\build\OpenCVConfig.cmake;D:\opencv\build\x64\vc15\lib\OpenCVConfig.cmake;D:\opencv\build\x64\vc15\lib\OpenCVModules-debug.cmake;D:\opencv\build\x64\vc15\lib\OpenCVModules-release.cmake;D:\opencv\build\x64\vc15\lib\OpenCVModules.cmake;E:\study\AI_deploy\TensorRT\yolov8_seg\build\CMakeFiles\3.31.6\CMakeCUDACompiler.cmake;E:\study\AI_deploy\TensorRT\yolov8_seg\build\CMakeFiles\3.31.6\CMakeCXXCompiler.cmake;E:\study\AI_deploy\TensorRT\yolov8_seg\build\CMakeFiles\3.31.6\CMakeRCCompiler.cmake;E:\study\AI_deploy\TensorRT\yolov8_seg\build\CMakeFiles\3.31.6\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\study\AI_deploy\TensorRT\yolov8_seg\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule E:/study/AI_deploy/TensorRT/yolov8_seg/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\cmake-3.31.6-windows-x86_64\bin\cmake.exe -SE:/study/AI_deploy/TensorRT/yolov8_seg -BE:/study/AI_deploy/TensorRT/yolov8_seg/build --check-stamp-file E:/study/AI_deploy/TensorRT/yolov8_seg/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCUDACompiler.cmake.in;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCUDACompilerABI.cu;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCUDAInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCXXCompiler.cmake.in;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCXXCompilerABI.cpp;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeCompilerIdDetection.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCUDACompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCXXCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCompilerABI.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCompilerId.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineCompilerSupport.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineRCCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeDetermineSystem.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeFindBinUtils.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeParseImplicitIncludeInfo.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeParseLibraryArchitecture.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeRCCompiler.cmake.in;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeRCInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeSystem.cmake.in;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeTestCUDACompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeTestCXXCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeTestCompilerCommon.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CMakeTestRCCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\ADSP-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\ARMCC-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\ARMClang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\AppleClang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Borland-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Cray-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\CrayClang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\GHS-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\GNU-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\HP-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IAR-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Intel-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\LCC-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\MSVC-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\MSVC.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\NVHPC-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\NVIDIA-CUDA.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\NVIDIA.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\OrangeC-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\PGI-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\PathScale-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\SCO-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\TI-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\TIClang-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Tasking-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\Watcom-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\XL-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\zOS-C-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\CompilerId\VS-10.vcxproj.in;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\FindCUDAToolkit.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\FindPackageMessage.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCUDAArchitecturesAll.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCUDAArchitecturesNative.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCUDAArchitecturesValidate.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCUDAFilterImplicitLibs.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCUDALinkerInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeDetermineLinkerId.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeNVCCFilterImplicitInfo.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\CMakeNVCCParseImplicitInfo.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Internal\FeatureTesting.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CUDA.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows-Determine-CXX.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows-NVIDIA-CUDA.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\Windows.cmake;D:\cmake-3.31.6-windows-x86_64\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;D:\opencv\build\OpenCVConfig-version.cmake;D:\opencv\build\OpenCVConfig.cmake;D:\opencv\build\x64\vc15\lib\OpenCVConfig.cmake;D:\opencv\build\x64\vc15\lib\OpenCVModules-debug.cmake;D:\opencv\build\x64\vc15\lib\OpenCVModules-release.cmake;D:\opencv\build\x64\vc15\lib\OpenCVModules.cmake;E:\study\AI_deploy\TensorRT\yolov8_seg\build\CMakeFiles\3.31.6\CMakeCUDACompiler.cmake;E:\study\AI_deploy\TensorRT\yolov8_seg\build\CMakeFiles\3.31.6\CMakeCXXCompiler.cmake;E:\study\AI_deploy\TensorRT\yolov8_seg\build\CMakeFiles\3.31.6\CMakeRCCompiler.cmake;E:\study\AI_deploy\TensorRT\yolov8_seg\build\CMakeFiles\3.31.6\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\study\AI_deploy\TensorRT\yolov8_seg\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="E:\study\AI_deploy\TensorRT\yolov8_seg\src\main.cpp" />
    <ClCompile Include="E:\study\AI_deploy\TensorRT\yolov8_seg\src\yolov8_seg_trt.cpp" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="E:\study\AI_deploy\TensorRT\yolov8_seg\build\ZERO_CHECK.vcxproj">
      <Project>{57157E2E-900C-35DC-A8D5-F663DC742A7C}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
    <Import Project="$(VCTargetsPath)\BuildCustomizations\CUDA 11.8.targets" />
  </ImportGroup>
</Project>