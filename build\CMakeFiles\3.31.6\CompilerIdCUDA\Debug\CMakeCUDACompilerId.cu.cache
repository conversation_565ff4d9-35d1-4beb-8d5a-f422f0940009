Identity=CMakeCUDACompilerId.cu
AdditionalCompilerOptions=
AdditionalCompilerOptions=
AdditionalDependencies=
AdditionalDeps=
AdditionalLibraryDirectories=
AdditionalOptions=-v -allow-unsupported-compiler
AdditionalOptions=-v -allow-unsupported-compiler
CodeGeneration=compute_52,sm_52
CodeGeneration=compute_52,sm_52
CompileOut=E:\study\AI_deploy\TensorRT\yolov8_seg\build\CMakeFiles\3.31.6\CompilerIdCUDA\CompilerIdCUDA\x64\Debug\CMakeCUDACompilerId.cu.obj
CudaRuntime=Static
CudaToolkitCustomDir=
DebugInformationFormat=ProgramDatabase
DebugInformationFormat=ProgramDatabase
Defines=;_MBCS;
Emulation=false
EnableVirtualArchInFatbin=true
ExtensibleWholeProgramCompilation=false
FastMath=false
GenerateLineInfo=false
GenerateRelocatableDeviceCode=false
GPUDebugInfo=true
GPUDebugInfo=true
HostDebugInfo=true
Include=;;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\include
Inputs=
InterleaveSourceInPTX=false
Keep=false
KeepDir=CompilerIdCUDA\x64\Debug
LinkOut=
MaxRegCount=0
NvccCompilation=compile
NvccPath=
Optimization=Od
Optimization=Od
PerformDeviceLink=
ProgramDataBaseFileName=Debug\vc143.pdb
ProgramDataBaseFileName=Debug\vc143.pdb
PtxAsOptionV=false
RequiredIncludes=
Runtime=MDd
Runtime=MDd
RuntimeChecks=RTC1
RuntimeChecks=RTC1
TargetMachinePlatform=64
TargetMachinePlatform=64
TypeInfo=
TypeInfo=
UseHostDefines=true
UseHostInclude=true
UseHostLibraryDependencies=
UseHostLibraryDirectories=
Warning=W0
Warning=W0
