#pragma once

#include <fstream>
#include <iostream>
#include <sstream>
#include <opencv2/opencv.hpp>
#include "NvInfer.h"

using namespace nvinfer1;
using namespace cv;

struct DetectResult
{
	int classId;
	float conf;
	cv::Rect box;
};

class YOLOv8TRTSegment 
{
	public:
		YOLOv8TRTSegment(){} // 显示声明构造函数
		void initConfig(std::string enginefile, float conf_threshold,float score_threshold);
		void detect(cv::Mat &frame,std::vector<DetectResult>& results);
		~YOLOv8TRTSegment();
	private:
		float sigmoid_function(float a);

		float conf_threshold = 0.25;
		float score_threshold = 0.25;
		int input_h = 640;
		int input_w = 640;
		int output_h;
		int output_w;
		int num_classes_plus_coords;
		int num_detections;
		int num_classes;
		int num_masks;

		IRuntime* runtime{nullptr};
		ICudaEngine* engine{nullptr};
		IExecutionContext* context{nullptr};
		void* buffers[3] = {nullptr, nullptr, nullptr}; // C++11引入 nullptr 类型安全

		std::vector<float>prob;
		std::vector<float>mprob;//mask
		cudaStream_t stream;
};

